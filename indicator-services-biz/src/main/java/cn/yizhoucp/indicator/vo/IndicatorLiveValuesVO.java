package cn.yizhoucp.indicator.vo;

import cn.yizhoucp.indicator.dto.BiTableFieldDTO;
import cn.yizhoucp.indicator.dto.BiTableViewDTO;
import cn.yizhoucp.indicator.dto.indicatorDTO.FunnelEvents;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorLiveValuesVO {

    /*数据面板类型*/
    private String analysisType;

    /*面板指标*/
    private List<String> indicatorNames;

    /*属性分组*/
    private Map<String, Set<String>> attributeGroup;

    /**
     * 条件组
     */
    private Map<String,String> controlGroup;

    /*多维表格属性分组*/
    private List<BiTableFieldDTO> multitableGroup;

    /*漏斗事件*/
    private List<FunnelEvents> funnelEvents;

    /*日期列*/
    private List<BiTableFieldDTO> dateColumn;

    /*数据列*/
    private List<BiTableFieldDTO> dataColumn;

    /*视图列*/
    private List<BiTableViewDTO> viewColumn;

}
