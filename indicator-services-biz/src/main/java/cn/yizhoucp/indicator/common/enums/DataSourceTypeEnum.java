package cn.yizhoucp.indicator.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Getter
public enum DataSourceTypeEnum {
    DATA_RANGER("dataRanger","dataRangerSource"),
    FEISHU_MULTITABLE("multlTable","multitableSource"),
    SUPER_SET("superSet","superSetSource");

    private String name;
    private String beanName;

    DataSourceTypeEnum(String name,String beanName) {
        this.name = name;
        this.beanName = beanName;
    }

    public static String getNameByCode(String dataSourceType){
        if(dataSourceType == null){
            return StrUtil.EMPTY;
        }
        for (DataSourceTypeEnum value : DataSourceTypeEnum.values()) {
            if (value.name.equals(dataSourceType)){
                return value.beanName;
            }
        }
        return StrUtil.EMPTY;
    }
}
