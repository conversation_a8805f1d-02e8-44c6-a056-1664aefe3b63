package cn.yizhoucp.indicator.manager.datasource;

import cn.hutool.core.util.StrUtil;
import cn.yizhoucp.indicator.config.http.OkHttpClientUtils;
import cn.yizhoucp.indicator.dto.indicatorDTO.DataSourceParams;
import cn.yizhoucp.indicator.dto.indicatorDTO.IndicatorConfigDTO;
import cn.yizhoucp.indicator.vo.IndicatorLiveValuesVO;
import cn.yizhoucp.indicator.vo.IndicatorResultVO;
import cn.yizhoucp.ms.core.base.ErrorCode;
import cn.yizhoucp.ms.core.base.ServiceException;
import cn.yizhoucp.ms.core.base.util.HttpClientUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component("superSetSource")
@Slf4j
public class SuperSetSource implements DataSourceStrategy{

    private static final String SUPERSET_HOST = "superset.cn.myrightone.com";
    private static final String LOGIN_URL = "https://superset.cn.myrightone.com/api/v1/security/login";
    private static final String FORM_DATA_URL = "https://superset.cn.myrightone.com/api/v1/explore/form_data/%s";

    @Resource
    private OkHttpClientUtils okHttpClientUtils;

    @Override
    public IndicatorLiveValuesVO getIndicatorLiveValues(DataSourceParams dataSourceParams) {
        String url = dataSourceParams.getReportUrl();
        log.info("SuperSetSource#getIndicatorLiveValues url: {}", url);

        try {
            // 1. 解析URL获取form_data_key
            String formDataKey = extractFormDataKey(url);
            log.info("SuperSetSource#getIndicatorLiveValues formDataKey: {}", formDataKey);

            // 2. 获取access_token
            String accessToken = getAccessToken();
            log.info("SuperSetSource#getIndicatorLiveValues accessToken: {}", accessToken);

            // 3. 获取form_data
            String formDataJson = getFormData(formDataKey, accessToken);
            log.info("SuperSetSource#getIndicatorLiveValues formDataJson: {}", formDataJson);

            // 4. 解析all_columns
            List<String> allColumns = parseAllColumns(formDataJson);
            log.info("SuperSetSource#getIndicatorLiveValues allColumns: {}", allColumns);

            // 5. 构建返回对象
            IndicatorLiveValuesVO result = new IndicatorLiveValuesVO();
            Map<String, Set<String>> attributeGroup = new HashMap<>();
            if (allColumns != null && !allColumns.isEmpty()) {
                attributeGroup.put("all_columns", new HashSet<>(allColumns));
            }
            result.setAttributeGroup(attributeGroup);

            return result;

        } catch (Exception e) {
            log.error("SuperSetSource#getIndicatorLiveValues error", e);
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "获取SuperSet数据失败: " + e.getMessage());
        }
    }

    @Override
    public IndicatorResultVO getIndicatorResult(IndicatorConfigDTO dataSourceParams) {
        return null;
    }

    /**
     * 从SuperSet URL中提取form_data_key
     * @param url SuperSet URL
     * @return form_data_key
     */
    private String extractFormDataKey(String url) {
        try {
            URL urlObj = new URL(url);
            if (!SUPERSET_HOST.equals(urlObj.getHost())) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "不是有效的SuperSet链接");
            }

            String query = urlObj.getQuery();
            if (StringUtils.isBlank(query)) {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "URL中缺少查询参数");
            }

            // 使用正则表达式匹配form_data_key参数
            Pattern pattern = Pattern.compile("form_data_key=([^&]+)");
            Matcher matcher = pattern.matcher(query);

            if (matcher.find()) {
                return matcher.group(1);
            } else {
                throw new ServiceException(ErrorCode.INVALID_PARAM, "URL中缺少form_data_key参数");
            }

        } catch (Exception e) {
            log.error("SuperSetSource#extractFormDataKey error", e);
            throw new ServiceException(ErrorCode.INVALID_PARAM, "解析SuperSet URL失败: " + e.getMessage());
        }
    }

    /**
     * 获取SuperSet访问令牌
     * @return access_token
     */
    private String getAccessToken() {
        try {
            // 构建登录请求体
            Map<String, String> loginData = new HashMap<>();
            loginData.put("username", "your_username"); // 需要配置实际的用户名
            loginData.put("password", "your_password"); // 需要配置实际的密码
            loginData.put("provider", "db"); // 默认数据库认证

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            String response = HttpClientUtil.doPostJsonAndSetHeaders(
                LOGIN_URL,
                JSON.toJSONString(loginData),
                headers
            );

            if (StringUtils.isBlank(response)) {
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "SuperSet登录响应为空");
            }

            JSONObject responseJson = JSONObject.parseObject(response);
            String accessToken = responseJson.getString("access_token");

            if (StringUtils.isBlank(accessToken)) {
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "获取SuperSet access_token失败");
            }

            return accessToken;

        } catch (Exception e) {
            log.error("SuperSetSource#getAccessToken error", e);
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "获取SuperSet访问令牌失败: " + e.getMessage());
        }
    }

    /**
     * 获取form_data数据
     * @param formDataKey form_data_key
     * @param accessToken 访问令牌
     * @return form_data JSON字符串
     */
    private String getFormData(String formDataKey, String accessToken) {
        try {
            String url = String.format(FORM_DATA_URL, formDataKey);

            // 使用OkHttpClientUtils的doGet方法，headers参数为String数组格式
            String[] headers = {
                "Authorization", "Bearer " + accessToken,
                "Content-Type", "application/json"
            };

            String response = okHttpClientUtils.doGet(url, headers);

            if (StringUtils.isBlank(response)) {
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "获取form_data响应为空");
            }

            JSONObject responseJson = JSONObject.parseObject(response);
            String formData = responseJson.getString("form_data");

            if (StringUtils.isBlank(formData)) {
                throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "form_data为空");
            }

            return formData;

        } catch (Exception e) {
            log.error("SuperSetSource#getFormData error", e);
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "获取form_data失败: " + e.getMessage());
        }
    }

    /**
     * 解析form_data中的all_columns
     * @param formDataJson form_data JSON字符串
     * @return all_columns列表
     */
    private List<String> parseAllColumns(String formDataJson) {
        try {
            JSONObject formDataObj = JSONObject.parseObject(formDataJson);
            JSONArray allColumnsArray = formDataObj.getJSONArray("all_columns");

            if (allColumnsArray == null || allColumnsArray.isEmpty()) {
                log.warn("SuperSetSource#parseAllColumns all_columns为空");
                return new ArrayList<>();
            }

            List<String> allColumns = new ArrayList<>();
            for (int i = 0; i < allColumnsArray.size(); i++) {
                String column = allColumnsArray.getString(i);
                if (StringUtils.isNotBlank(column)) {
                    allColumns.add(column);
                }
            }

            return allColumns;

        } catch (Exception e) {
            log.error("SuperSetSource#parseAllColumns error", e);
            throw new ServiceException(ErrorCode.SERVER_EXCEPTION, "解析all_columns失败: " + e.getMessage());
        }
    }
}
