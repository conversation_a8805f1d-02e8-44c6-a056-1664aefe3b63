package cn.yizhoucp.indicator.manager.datasource;

import cn.yizhoucp.indicator.dto.indicatorDTO.DataSourceParams;
import cn.yizhoucp.indicator.dto.indicatorDTO.IndicatorConfigDTO;
import cn.yizhoucp.indicator.vo.IndicatorLiveValuesVO;
import cn.yizhoucp.indicator.vo.IndicatorResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SuperSetSource implements DataSourceStrategy{


    @Override
    public IndicatorLiveValuesVO getIndicatorLiveValues(DataSourceParams dataSourceParams) {
        String url= dataSourceParams.getReportUrl();
        return null;
    }

    @Override
    public IndicatorResultVO getIndicatorResult(IndicatorConfigDTO dataSourceParams) {
        return null;
    }
}
