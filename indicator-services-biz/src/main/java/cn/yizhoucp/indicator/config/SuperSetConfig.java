package cn.yizhoucp.indicator.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SuperSet配置类
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "superset")
public class SuperSetConfig {

    /**
     * SuperSet用户名
     */
    private String username = "admin";

    /**
     * SuperSet密码
     */
    private String password = "admin";

    /**
     * SuperSet主机地址
     */
    private String host = "superset.cn.myrightone.com";

    /**
     * 登录URL
     */
    private String loginUrl = "https://superset.cn.myrightone.com/api/v1/security/login";

    /**
     * form_data获取URL模板
     */
    private String formDataUrlTemplate = "https://superset.cn.myrightone.com/api/v1/explore/form_data/%s";
}
